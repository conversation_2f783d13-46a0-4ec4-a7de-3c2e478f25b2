<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>xing2006.me - 数字生活空间</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌟</text></svg>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 动态背景粒子 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 60% 70%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
            pointer-events: none;
            animation: particleFloat 15s ease-in-out infinite;
        }

        @keyframes particleFloat {
            0%, 100% { opacity: 1; transform: translateY(0px); }
            50% { opacity: 0.8; transform: translateY(-10px); }
        }

        /* 浮动装饰元素 */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 30%;
            right: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .container {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid var(--glass-border);
            border-radius: 32px;
            padding: 50px;
            box-shadow: var(--shadow-heavy);
            backdrop-filter: blur(30px);
            max-width: 500px;
            width: 100%;
            position: relative;
            animation: containerFadeIn 1s ease-out;
            overflow: hidden;
        }

        /* 容器内部光效 */
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
        }
        
        @keyframes containerFadeIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .header {
            text-align: center;
            margin-bottom: 45px;
            position: relative;
        }
        
        .logo {
            font-size: 36px;
            font-weight: 800;
            color: white;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            letter-spacing: -1px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .logo-icon {
            animation: logoSpin 4s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.5));
        }
        
        @keyframes logoSpin {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(5deg) scale(1.1); }
            75% { transform: rotate(-5deg) scale(1.1); }
        }
        
        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 400;
            line-height: 1.6;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .services-grid {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .service-item {
            display: flex;
            align-items: center;
            padding: 24px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
            animation: serviceItemFadeIn 0.8s ease-out forwards;
            opacity: 0;
            transform: translateX(-30px);
            box-shadow: var(--shadow-light);
        }
        
        .service-item:nth-child(1) { animation-delay: 0.1s; }
        .service-item:nth-child(2) { animation-delay: 0.2s; }
        .service-item:nth-child(3) { animation-delay: 0.3s; }
        .service-item:nth-child(4) { animation-delay: 0.4s; }
        .service-item:nth-child(5) { animation-delay: 0.5s; }
        
        @keyframes serviceItemFadeIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* 悬浮光效 */
        .service-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            transition: left 0.8s;
        }
        
        .service-item:hover::before {
            left: 100%;
        }
        
        .service-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-6px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            border-color: rgba(255,255,255,0.8);
        }
        
        .service-item:active {
            transform: translateY(-3px) scale(1.01);
        }
        
        .service-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            color: white;
            font-weight: 600;
            position: relative;
            box-shadow: var(--shadow-medium);
            transition: all 0.4s ease;
        }
        
        .service-item:hover .service-icon {
            transform: scale(1.15) rotate(8deg);
            box-shadow: var(--shadow-heavy);
        }
        
        .service-info {
            flex: 1;
            min-width: 0;
        }
        
        .service-name {
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 8px;
            color: var(--text-primary);
            letter-spacing: -0.3px;
        }
        
        .service-desc {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .service-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #10b981;
            margin-left: 15px;
            position: relative;
            flex-shrink: 0;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }
        
        .service-status::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #10b981;
            animation: statusPulse 2s infinite;
        }
        
        @keyframes statusPulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(2.5);
                opacity: 0;
            }
        }
        
        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
            color: white;
        }
        
        .stat-number {
            font-weight: 800;
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
        }
        
        .footer {
            text-align: center;
            margin-top: 35px;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .footer-content {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 13px;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .footer-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 35px 25px;
                margin: 15px;
                border-radius: 24px;
            }
            
            .service-item {
                padding: 20px;
            }
            
            .service-icon {
                width: 48px;
                height: 48px;
                font-size: 22px;
                margin-right: 16px;
            }
            
            .service-name {
                font-size: 16px;
            }
            
            .service-desc {
                font-size: 13px;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 10px;
            }
            
            .stats {
                padding: 16px;
            }
            
            .stat-number {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="container">
        <div class="header">
            <div class="logo">
                <span class="logo-icon">🌟</span>
                xing2006.me
            </div>
            <div class="subtitle">欢迎来到我的数字化生活空间</div>
        </div>
        
        <div class="services-grid">
            <a href="https://yu.xing2006.me" class="service-item" target="_blank">
                <div class="service-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">🤖</div>
                <div class="service-info">
                    <div class="service-name">Open WebUI</div>
                    <div class="service-desc">智能AI对话平台，支持多模型交互</div>
                </div>
                <div class="service-status"></div>
            </a>
            
            <a href="https://gemini.xing2006.me" class="service-item" target="_blank">
                <div class="service-icon" style="background: linear-gradient(135deg, #4285f4, #1a73e8);">🔮</div>
                <div class="service-info">
                    <div class="service-name">Gemini 服务</div>
                    <div class="service-desc">Google Gemini AI助手平台</div>
                </div>
                <div class="service-status"></div>
            </a>
            
            <a href="https://blog.xing2006.me" class="service-item" target="_blank">
                <div class="service-icon" style="background: linear-gradient(135deg, #6f42c1, #5a32a3);">📝</div>
                <div class="service-info">
                    <div class="service-name">Ghost 博客</div>
                    <div class="service-desc">个人技术博客与思考分享</div>
                </div>
                <div class="service-status"></div>
            </a>
            
            <a href="https://cloud.xing2006.me" class="service-item" target="_blank">
                <div class="service-icon" style="background: linear-gradient(135deg, #0082c9, #005a8b);">☁️</div>
                <div class="service-info">
                    <div class="service-name">NextCloud</div>
                    <div class="service-desc">私有云存储与文件同步服务</div>
                </div>
                <div class="service-status"></div>
            </a>
            
            <a href="https://see.xing2006.me/f0c87f5752" class="service-item" target="_blank">
                <div class="service-icon" style="background: linear-gradient(135deg, #ff6b35, #f7931e);">🛠️</div>
                <div class="service-info">
                    <div class="service-name">1Panel 面板</div>
                    <div class="service-desc">服务器管理与运维控制面板</div>
                </div>
                <div class="service-status"></div>
            </a>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <span class="stat-number">5</span>
                <span class="stat-label">活跃服务</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">24/7</span>
                <span class="stat-label">在线时间</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">99.9%</span>
                <span class="stat-label">可用性</span>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-content">
                Powered by xing2006 © 2025
            </div>
            <div class="footer-links">
                <a href="mailto:<EMAIL>" class="footer-link">联系我</a>
                <a href="#" class="footer-link">服务状态</a>
                <a href="#" class="footer-link">使用条款</a>
            </div>
        </div>
    </div>
</body>
</html>
